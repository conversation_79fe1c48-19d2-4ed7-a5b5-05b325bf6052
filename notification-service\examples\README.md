# WebSocket Implementation Examples

Direktori ini berisi contoh implementasi WebSocket client untuk berbagai framework.

## Available Examples

Saat ini belum ada contoh implementasi. <PERSON><PERSON><PERSON> refer ke [WEBSOCKET_MANUAL.md](../WEBSOCKET_MANUAL.md) untuk panduan implementasi.

## Framework Support

Manual mendukung implementasi untuk:
- React (dengan hooks)
- Vue.js (dengan composables)
- Angular (dengan services)
- Vanilla JavaScript

## Quick Reference

### Basic Connection Flow
1. Install `socket.io-client`
2. Connect ke `http://localhost:3000` (via API Gateway - recommended)
3. Emit `authenticate` dengan JWT token
4. Listen untuk notification events

### Key Events
- `analysis-started`: Analisis dimulai
- `analysis-complete`: Ana<PERSON><PERSON> selesai
- `analysis-failed`: Ana<PERSON>is gagal

Untuk detail lengkap, lihat [WEBSOCKET_MANUAL.md](../WEBSOCKET_MANUAL.md).
